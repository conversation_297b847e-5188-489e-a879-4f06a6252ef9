import React from 'react';
import {
  FocusLayoutContainer,
  GridLayout,
  LayoutContextProvider,
  RoomAudioRenderer,
  TrackRefContext,
} from "@livekit/components-react";
import { ParticipantTile } from "../customFabs/ParticipantTile";
import { ControlBar } from "../customFabs/ControlBar";

export function PipVideoConference({
  layoutContext,
  focusTrack,
  carouselTracks,
  room,
  tracks,
  screenShareTracks,
  isForceMuteAll,
  isCoHost,
  isHost,
  isForceVideoOffAll,
  isScreenShareEnabled,
  onScreenShareChange,
  screenShareMode,
  maxWidth,
  maxHeight,
  setIsPIPEnabled,
  sipData,
  isWhiteboardOpen,
  setIsWhiteboardOpen,
  screenShareSources,
  isElectronApp,
  isExitWhiteboardModalOpen,
  setIsExitWhiteboardModalOpen,
  whiteboardSceneData,
  setWhiteboardSceneData,
  whiteBoardId,
  setWhiteboardId,
  setScreenShareDisplayId,
  setIsScreenShareEnabled,
  setScreenShareMode,
  ...props
}) {
  return (
    <div 
      className="pip-video-conference"
      style={{
        width: '100%',
        height: '100vh',
        display: 'flex',
        flexDirection: 'column',
        background: '#000',
        overflow: 'hidden'
      }}
    >
      <LayoutContextProvider value={layoutContext}>
        <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          {focusTrack ? (
            <FocusLayoutContainer>
              <TrackRefContext.Provider value={focusTrack}>
                <ParticipantTile trackRef={focusTrack} />
              </TrackRefContext.Provider>
              {carouselTracks.length > 0 && (
                <div style={{ 
                  height: '120px', 
                  display: 'flex', 
                  gap: '8px', 
                  padding: '8px',
                  overflowX: 'auto'
                }}>
                  {carouselTracks.map((track, index) => (
                    <TrackRefContext.Provider 
                      value={track} 
                      key={track.participant.identity || `track-${index}`}
                    >
                      <div style={{ minWidth: '160px', height: '100%' }}>
                        <ParticipantTile trackRef={track} />
                      </div>
                    </TrackRefContext.Provider>
                  ))}
                </div>
              )}
            </FocusLayoutContainer>
          ) : (
            <GridLayout tracks={carouselTracks}>
              {carouselTracks.map((track, index) => (
                <TrackRefContext.Provider 
                  value={track} 
                  key={track.participant.identity || `track-${index}`}
                >
                  <ParticipantTile trackRef={track} />
                </TrackRefContext.Provider>
              ))}
            </GridLayout>
          )}
        </div>

        {/* Control Bar */}
        <div style={{ 
          padding: '8px',
          background: 'rgba(0, 0, 0, 0.8)',
          borderTop: '1px solid rgba(255, 255, 255, 0.1)'
        }}>
          <ControlBar
            variation="minimal"
            controls={{
              microphone: true,
              camera: true,
              screenShare: isScreenShareEnabled,
              chat: false,
              settings: false,
              leave: true,
            }}
            saveUserChoices={false}
            isForceMuteAll={isForceMuteAll}
            isCoHost={isCoHost}
            isHost={isHost}
            isForceVideoOffAll={isForceVideoOffAll}
            isScreenShareEnabled={isScreenShareEnabled}
            onScreenShareChange={onScreenShareChange}
            screenShareMode={screenShareMode}
            maxWidth={maxWidth}
            maxHeight={maxHeight}
            setIsPIPEnabled={setIsPIPEnabled}
            sipData={sipData}
            isWhiteboardOpen={isWhiteboardOpen}
            setIsWhiteboardOpen={setIsWhiteboardOpen}
            screenShareSources={screenShareSources}
            isElectronApp={isElectronApp}
            isExitWhiteboardModalOpen={isExitWhiteboardModalOpen}
            setIsExitWhiteboardModalOpen={setIsExitWhiteboardModalOpen}
            whiteboardSceneData={whiteboardSceneData}
            setWhiteboardSceneData={setWhiteboardSceneData}
            whiteBoardId={whiteBoardId}
            setWhiteboardId={setWhiteboardId}
            setScreenShareDisplayId={setScreenShareDisplayId}
            setIsScreenShareEnabled={setIsScreenShareEnabled}
            setScreenShareMode={setScreenShareMode}
            {...props}
          />
        </div>
      </LayoutContextProvider>
      
      <RoomAudioRenderer />
    </div>
  );
}
